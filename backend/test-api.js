// Simple test script to verify API endpoints
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api/auth';

async function testAPI() {
    console.log('🧪 Testing Backend API Endpoints...\n');

    try {
        // Test 1: Admin Login
        console.log('1. Testing Admin Login (admin/1234)...');
        const adminLoginResponse = await fetch(`${BASE_URL}/signin`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: 'admin',
                password: '1234'
            })
        });

        const adminLoginData = await adminLoginResponse.json();
        console.log('✅ Admin Login Response:', adminLoginData);
        
        if (adminLoginData.success) {
            console.log('🔑 Admin Token:', adminLoginData.token.substring(0, 20) + '...');
        }

        // Test 2: User Registration
        console.log('\n2. Testing User Registration...');
        const signupResponse = await fetch(`${BASE_URL}/signup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: 'Test User',
                email: '<EMAIL>',
                password: 'password123'
            })
        });

        const signupData = await signupResponse.json();
        console.log('✅ Signup Response:', signupData);

        // Test 3: User Login
        if (signupData.success) {
            console.log('\n3. Testing User Login...');
            const loginResponse = await fetch(`${BASE_URL}/signin`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: '<EMAIL>',
                    password: 'password123'
                })
            });

            const loginData = await loginResponse.json();
            console.log('✅ User Login Response:', loginData);
            
            if (loginData.success) {
                console.log('🔑 User Token:', loginData.token.substring(0, 20) + '...');
            }
        }

        console.log('\n🎉 API Testing Complete!');

    } catch (error) {
        console.error('❌ API Test Error:', error.message);
    }
}

// Run the test
testAPI();
