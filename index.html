<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MessageMe - Welcome</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .auth-container {
            position: relative;
            width: 400px;
            max-width: 90vw;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .auth-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 35px 60px rgba(0, 0, 0, 0.3);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: white;
            font-size: 2.5em;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 10px;
        }

        .logo p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9em;
            font-weight: 300;
        }

        .form-container {
            position: relative;
            overflow: hidden;
        }

        .form {
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .form.active {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            color: white;
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        input[type="text"],
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="password"]:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.4);
            transform: scale(1.02);
        }

        input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(238, 90, 36, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(238, 90, 36, 0.4);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .toggle-container {
            text-align: center;
            margin-top: 25px;
        }

        .toggle-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9em;
        }

        .toggle-link {
            color: #ff6b6b;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .toggle-link:hover {
            color: #ee5a24;
            text-decoration: underline;
        }

        .social-login {
            margin-top: 30px;
            text-align: center;
        }

        .social-divider {
            color: rgba(255, 255, 255, 0.6);
            margin: 20px 0;
            font-size: 0.9em;
        }

        .social-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .social-btn {
            flex: 1;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .social-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .error-message {
            color: #ff6b6b;
            font-size: 0.8em;
            margin-top: 5px;
            display: none;
        }

        .success-message {
            color: #2ed573;
            font-size: 0.8em;
            margin-top: 5px;
            display: none;
        }

        @media (max-width: 480px) {
            .auth-container {
                padding: 30px 20px;
            }
            
            .logo h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="particles"></div>
    
    <div class="auth-container">
        <div class="logo">
            <h1>MessageMe</h1>
            <p>Connect, Chat, Share</p>
        </div>

        <div class="form-container">
            <!-- Sign In Form -->
            <form id="signin-form" class="form active">
                <div class="form-group">
                    <label for="signin-email">Email Address</label>
                    <input type="email" id="signin-email" placeholder="Enter your email" required>
                    <div class="error-message" id="signin-email-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="signin-password">Password</label>
                    <input type="password" id="signin-password" placeholder="Enter your password" required>
                    <div class="error-message" id="signin-password-error"></div>
                </div>
                
                <button type="submit" class="submit-btn">Sign In</button>
                <div class="success-message" id="signin-success"></div>
            </form>

            <!-- Sign Up Form -->
            <form id="signup-form" class="form">
                <div class="form-group">
                    <label for="signup-name">Full Name</label>
                    <input type="text" id="signup-name" placeholder="Enter your full name" required>
                    <div class="error-message" id="signup-name-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="signup-email">Email Address</label>
                    <input type="email" id="signup-email" placeholder="Enter your email" required>
                    <div class="error-message" id="signup-email-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="signup-password">Password</label>
                    <input type="password" id="signup-password" placeholder="Create a password" required>
                    <div class="error-message" id="signup-password-error"></div>
                </div>
                
                <div class="form-group">
                    <label for="signup-confirm">Confirm Password</label>
                    <input type="password" id="signup-confirm" placeholder="Confirm your password" required>
                    <div class="error-message" id="signup-confirm-error"></div>
                </div>
                
                <button type="submit" class="submit-btn">Sign Up</button>
                <div class="success-message" id="signup-success"></div>
            </form>
        </div>

        <div class="toggle-container">
            <span class="toggle-text" id="toggle-text">Don't have an account? </span>
            <a href="#" class="toggle-link" id="toggle-link">Sign Up</a>
        </div>

        <div class="social-login">
            <div class="social-divider">or continue with</div>
            <div class="social-buttons">
                <a href="#" class="social-btn">Google</a>
                <a href="#" class="social-btn">Facebook</a>
            </div>
        </div>
    </div>

    <script>
        class AuthManager {
            constructor() {
                this.isSignIn = true;
                this.initializeElements();
                this.attachEventListeners();
                this.createParticles();
            }

            initializeElements() {
                this.signinForm = document.getElementById('signin-form');
                this.signupForm = document.getElementById('signup-form');
                this.toggleLink = document.getElementById('toggle-link');
                this.toggleText = document.getElementById('toggle-text');
            }

            attachEventListeners() {
                this.toggleLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.toggleForm();
                });

                this.signinForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleSignIn();
                });

                this.signupForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleSignUp();
                });

                // Real-time validation
                document.querySelectorAll('input').forEach(input => {
                    input.addEventListener('blur', () => this.validateField(input));
                });
            }

            toggleForm() {
                this.isSignIn = !this.isSignIn;
                
                if (this.isSignIn) {
                    this.signinForm.classList.add('active');
                    this.signupForm.classList.remove('active');
                    this.toggleText.textContent = "Don't have an account? ";
                    this.toggleLink.textContent = "Sign Up";
                } else {
                    this.signinForm.classList.remove('active');
                    this.signupForm.classList.add('active');
                    this.toggleText.textContent = "Already have an account? ";
                    this.toggleLink.textContent = "Sign In";
                }
                
                this.clearMessages();
            }

            validateField(field) {
                const value = field.value.trim();
                const errorElement = document.getElementById(field.id + '-error');
                
                switch (field.type) {
                    case 'email':
                        if (!this.isValidEmail(value)) {
                            this.showError(errorElement, 'Please enter a valid email address');
                            return false;
                        }
                        break;
                    case 'password':
                        if (value.length < 6) {
                            this.showError(errorElement, 'Password must be at least 6 characters');
                            return false;
                        }
                        break;
                    case 'text':
                        if (value.length < 2) {
                            this.showError(errorElement, 'Name must be at least 2 characters');
                            return false;
                        }
                        break;
                }
                
                // Check password confirmation
                if (field.id === 'signup-confirm') {
                    const password = document.getElementById('signup-password').value;
                    if (value !== password) {
                        this.showError(errorElement, 'Passwords do not match');
                        return false;
                    }
                }
                
                this.hideError(errorElement);
                return true;
            }

            handleSignIn() {
                const email = document.getElementById('signin-email').value.trim();
                const password = document.getElementById('signin-password').value;
                
                if (!this.validateForm('signin')) return;
                
                // Simulate API call
                this.showLoading(true);
                
                setTimeout(() => {
                    this.showLoading(false);
                    
                    // Simulate successful sign in
                    if (email && password) {
                        this.showSuccess('signin-success', 'Welcome back! Redirecting...');
                        setTimeout(() => {
                            // Redirect to chat application
                            console.log('Redirecting to chat...');
                        }, 2000);
                    }
                }, 1500);
            }

            handleSignUp() {
                if (!this.validateForm('signup')) return;
                
                const name = document.getElementById('signup-name').value.trim();
                const email = document.getElementById('signup-email').value.trim();
                const password = document.getElementById('signup-password').value;
                
                // Simulate API call
                this.showLoading(true);
                
                setTimeout(() => {
                    this.showLoading(false);
                    
                    // Simulate successful sign up
                    if (name && email && password) {
                        this.showSuccess('signup-success', 'Account created successfully! Please check your email.');
                        setTimeout(() => {
                            this.toggleForm();
                        }, 2000);
                    }
                }, 1500);
            }

            validateForm(type) {
                const form = document.getElementById(type + '-form');
                const inputs = form.querySelectorAll('input');
                let isValid = true;
                
                inputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isValid = false;
                    }
                });
                
                return isValid;
            }

            showError(element, message) {
                element.textContent = message;
                element.style.display = 'block';
            }

            hideError(element) {
                element.style.display = 'none';
            }

            showSuccess(elementId, message) {
                const element = document.getElementById(elementId);
                element.textContent = message;
                element.style.display = 'block';
            }

            clearMessages() {
                document.querySelectorAll('.error-message, .success-message').forEach(el => {
                    el.style.display = 'none';
                });
            }

            showLoading(show) {
                const submitBtns = document.querySelectorAll('.submit-btn');
                submitBtns.forEach(btn => {
                    btn.disabled = show;
                    btn.textContent = show ? 'Please wait...' : (this.isSignIn ? 'Sign In' : 'Sign Up');
                });
            }

            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            createParticles() {
                const particlesContainer = document.querySelector('.particles');
                
                for (let i = 0; i < 20; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
                    particlesContainer.appendChild(particle);
                }
            }
        }

        // Initialize the auth manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new AuthManager();
        });
    </script>
</body>
</html>