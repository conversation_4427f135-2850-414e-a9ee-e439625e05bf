<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MessageMe - Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            height: 100vh;
            background: #ffffff;
        }

        /* Sidebar */
        .sidebar {
            width: 400px;
            background: #ffffff;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            min-width: 300px;
        }

        .sidebar-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }

        .user-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }

        .search-container {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 20px 12px 45px;
            border: 1px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            background: white;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #8e8e93;
            font-size: 16px;
        }

        .users-list {
            flex: 1;
            overflow-y: auto;
            background: white;
        }

        .user-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-item:hover {
            background: #f8f9fa;
        }

        .user-item.active {
            background: #e3f2fd;
            border-right: 3px solid #667eea;
        }

        .user-item-avatar {
            position: relative;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        .online-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            background: #4caf50;
        }

        .offline-indicator {
            background: #9e9e9e;
        }

        .user-item-info {
            flex: 1;
        }

        .user-item-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .user-item-status {
            font-size: 14px;
            color: #7f8c8d;
        }

        .last-message {
            font-size: 13px;
            color: #95a5a6;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        .message-time {
            font-size: 12px;
            color: #95a5a6;
            margin-left: auto;
        }

        .unread-count {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }

        /* Main Chat Area */
        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #ffffff;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chat-user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        .chat-user-info h3 {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 2px;
        }

        .chat-user-info p {
            font-size: 14px;
            color: #7f8c8d;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23e0e0e0" opacity="0.1"/></svg>');
            background-size: 20px 20px;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }

        .message.sent {
            justify-content: flex-end;
        }

        .message.received {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            animation: messageSlide 0.3s ease-out;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.sent .message-bubble {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.received .message-bubble {
            background: white;
            color: #2c3e50;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .message-text {
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
        }

        .message.sent .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .message.received .message-time {
            color: #95a5a6;
        }

        .message-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .message-input {
            flex: 1;
            padding: 12px 20px;
            border: 1px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            resize: none;
            max-height: 120px;
            min-height: 45px;
            transition: border-color 0.3s ease;
        }

        .message-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .send-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            background: #5a67d8;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }

        .welcome-screen {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            text-align: center;
        }

        .welcome-icon {
            font-size: 80px;
            margin-bottom: 20px;
            color: #cbd5e0;
        }

        .welcome-screen h2 {
            font-size: 32px;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .welcome-screen p {
            font-size: 16px;
            max-width: 400px;
            line-height: 1.6;
        }

        .typing-indicator {
            padding: 10px 20px;
            background: rgba(103, 126, 234, 0.1);
            border-radius: 15px;
            margin-bottom: 10px;
            display: none;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: fixed;
                left: 0;
                top: 0;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-chat {
                width: 100%;
            }

            .mobile-menu-btn {
                display: block;
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                padding: 10px;
                color: #2c3e50;
            }
        }

        @media (min-width: 769px) {
            .mobile-menu-btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="user-info">
                    <div class="user-avatar">JD</div>
                    <div class="user-name">John Doe</div>
                </div>
                <button class="logout-btn" onclick="logout()">Logout</button>
            </div>

            <div class="search-container">
                <div class="search-box">
                    <span class="search-icon">🔍</span>
                    <input type="text" class="search-input" placeholder="Search users..." id="searchInput">
                </div>
            </div>

            <div class="users-list" id="usersList">
                <!-- Users will be populated here -->
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="main-chat" id="mainChat">
            <div class="welcome-screen" id="welcomeScreen">
                <div class="welcome-icon">💬</div>
                <h2>Welcome to MessageMe</h2>
                <p>Select a user from the sidebar to start chatting. Connect with friends and share your thoughts instantly!</p>
            </div>

            <div class="chat-area" id="chatArea" style="display: none;">
                <div class="chat-header">
                    <button class="mobile-menu-btn" onclick="toggleSidebar()">☰</button>
                    <div class="chat-user-avatar" id="chatUserAvatar">A</div>
                    <div class="chat-user-info">
                        <h3 id="chatUserName">User Name</h3>
                        <p id="chatUserStatus">Online</p>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="typing-indicator" id="typingIndicator">
                        <div class="typing-dots">
                            <span class="typing-dot"></span>
                            <span class="typing-dot"></span>
                            <span class="typing-dot"></span>
                            <span style="margin-left: 8px; font-size: 12px; color: #667eea;">typing...</span>
                        </div>
                    </div>
                </div>

                <div class="message-input-container">
                    <textarea class="message-input" id="messageInput" placeholder="Type a message..." rows="1"></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">➤</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class MessageMeChat {
            constructor() {
                this.currentUser = null;
                this.users = [];
                this.messages = {};
                this.isTyping = false;
                
                this.initializeApp();
                this.generateMockUsers();
                this.attachEventListeners();
            }

            initializeApp() {
                // Initialize elements
                this.usersList = document.getElementById('usersList');
                this.chatArea = document.getElementById('chatArea');
                this.welcomeScreen = document.getElementById('welcomeScreen');
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.searchInput = document.getElementById('searchInput');
                this.typingIndicator = document.getElementById('typingIndicator');
            }

            generateMockUsers() {
                this.users = [
                    {
                        id: 1,
                        name: "Alice Johnson",
                        avatar: "AJ",
                        status: "online",
                        lastMessage: "Hey! How are you doing?",
                        lastMessageTime: "2 min ago",
                        unreadCount: 3
                    },
                    {
                        id: 2,
                        name: "Bob Smith",
                        avatar: "BS",
                        status: "offline",
                        lastMessage: "Thanks for the help!",
                        lastMessageTime: "1 hour ago",
                        unreadCount: 0
                    },
                    {
                        id: 3,
                        name: "Carol Davis",
                        avatar: "CD",
                        status: "online",
                        lastMessage: "See you tomorrow 👋",
                        lastMessageTime: "3 hours ago",
                        unreadCount: 1
                    },
                    {
                        id: 4,
                        name: "David Wilson",
                        avatar: "DW",
                        status: "online",
                        lastMessage: "That sounds great!",
                        lastMessageTime: "1 day ago",
                        unreadCount: 0
                    },
                    {
                        id: 5,
                        name: "Emma Brown",
                        avatar: "EB",
                        status: "offline",
                        lastMessage: "Let's catch up soon",
                        lastMessageTime: "2 days ago",
                        unreadCount: 2
                    },
                    {
                        id: 6,
                        name: "Frank Miller",
                        avatar: "FM",
                        status: "online",
                        lastMessage: "Perfect! 🎉",
                        lastMessageTime: "1 week ago",
                        unreadCount: 0
                    }
                ];

                this.generateMockMessages();
                this.renderUsers();
            }

            generateMockMessages() {
                this.messages = {
                    1: [
                        { id: 1, text: "Hey! How are you doing?", sent: false, time: "10:30 AM" },
                        { id: 2, text: "I'm doing great, thanks for asking!", sent: true, time: "10:32 AM" },
                        { id: 3, text: "What about you?", sent: true, time: "10:32 AM" },
                        { id: 4, text: "I'm good too! Just working on some projects", sent: false, time: "10:35 AM" },
                        { id: 5, text: "That's awesome! What kind of projects?", sent: true, time: "10:36 AM" }
                    ],
                    2: [
                        { id: 1, text: "Thanks for the help with the presentation!", sent: false, time: "9:15 AM" },
                        { id: 2, text: "No problem! Happy to help", sent: true, time: "9:20 AM" },
                        { id: 3, text: "It went really well", sent: false, time: "2:30 PM" }
                    ],
                    3: [
                        { id: 1, text: "Are we still on for tomorrow?", sent: true, time: "8:00 AM" },
                        { id: 2, text: "Yes! Looking forward to it", sent: false, time: "8:05 AM" },
                        { id: 3, text: "See you tomorrow 👋", sent: false, time: "8:06 AM" }
                    ]
                };
            }

            renderUsers() {
                const filteredUsers = this.users.filter(user => 
                    user.name.toLowerCase().includes(this.searchInput.value.toLowerCase())
                );

                this.usersList.innerHTML = '';
                
                filteredUsers.forEach(user => {
                    const userElement = document.createElement('div');
                    userElement.className = `user-item ${this.currentUser?.id === user.id ? 'active' : ''}`;
                    userElement.onclick = () => this.selectUser(user);
                    
                    userElement.innerHTML = `
                        <div class="user-item-avatar">
                            ${user.avatar}
                            <div class="online-indicator ${user.status === 'offline' ? 'offline-indicator' : ''}"></div>
                        </div>
                        <div class="user-item-info">
                            <div class="user-item-name">${user.name}</div>
                            <div class="last-message">${user.lastMessage}</div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-end;">
                            <div class="message-time">${user.lastMessageTime}</div>
                            ${user.unreadCount > 0 ? `<div class="unread-count">${user.unreadCount}</div>` : ''}
                        </div>
                    `;
                    
                    this.usersList.appendChild(userElement);
                });
            }

            selectUser(user) {
                this.currentUser = user;
                this.welcomeScreen.style.display = 'none';
                this.chatArea.style.display = 'flex';
                
                // Update chat header
                document.getElementById('chatUserAvatar').textContent = user.avatar;
                document.getElementById('chatUserName').textContent = user.name;
                document.getElementById('chatUserStatus').textContent = user.status === 'online' ? 'Online' : 'Last seen recently';
                
                // Clear unread count
                user.unreadCount = 0;
                
                // Render messages
                this.renderMessages();
                this.renderUsers();
                
                // Close sidebar on mobile
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('open');
                }
            }

            renderMessages() {
                if (!this.currentUser) return;
                
                const userMessages = this.messages[this.currentUser.id] || [];
                this.chatMessages.innerHTML = '';
                
                userMessages.forEach(message => {
                    const messageElement = document.createElement('div');
                    messageElement.className = `message ${message.sent ? 'sent' : 'received'}`;
                    
                    messageElement.innerHTML = `
                        <div class="message-bubble">
                            <div class="message-text">${message.text}</div>
                            <div class="message-time">${message.time}</div>
                        </div>
                    `;
                    
                    this.chatMessages.appendChild(messageElement);
                });
                
                // Add typing indicator
                this.chatMessages.appendChild(this.typingIndicator);
                
                // Scroll to bottom
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            sendMessage() {
                if (!this.currentUser) return;
                
                const text = this.messageInput.value.trim();
                if (!text) return;
                
                const newMessage = {
                    id: Date.now(),
                    text: text,
                    sent: true,
                    time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                };
                
                // Add to messages
                if (!this.messages[this.currentUser.id]) {
                    this.messages[this.currentUser.id] = [];
                }
                this.messages[this.currentUser.id].push(newMessage);
                
                // Update user's last message
                this.currentUser.lastMessage = text;
                this.currentUser.lastMessageTime = 'now';
                
                // Clear input
                this.messageInput.value = '';
                this.messageInput.style.height = '45px';
                
                // Re-render
                this.renderMessages();
                this.renderUsers();
                
                // Simulate typing and response
                this.simulateTyping();
            }

            simulateTyping() {
                if (!this.currentUser) return;
                
                // Show typing indicator
                this.typingIndicator.style.display = 'block';
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                
                // Simulate response after 2-3 seconds
                setTimeout(() => {
                    this.typingIndicator.style.display = 'none';
                    
                    const responses = [
                        "That's interesting! 🤔",
                        "I totally agree with you",
                        "Thanks for sharing that!",
                        "Haha, that's funny! 😄",
                        "Really? Tell me more!",
                        "I see what you mean",
                        "That sounds great!",
                        "Absolutely! 👍"
                    ];
                    
                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                    
                    const responseMessage = {
                        id: Date.now(),
                        text: randomResponse,
                        sent: false,
                        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                    };
                    
                    this.messages[this.currentUser.id].push(responseMessage);
                    this.currentUser.lastMessage = randomResponse;
                    this.currentUser.lastMessageTime = 'now';
                    
                    this.renderMessages();
                    this.renderUsers();
                }, Math.random() * 2000 + 1000);
            }

            attachEventListeners() {
                // Search functionality
                this.searchInput.addEventListener('input', () => {
                    this.renderUsers();
                });

                // Message input auto-resize
                this.messageInput.addEventListener('input', () => {
                    this.messageInput.style.height = '45px';
                    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
                });

                // Send message on Enter
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // Update send button state
                this.messageInput.addEventListener('input', () => {
                    this.sendBtn.disabled = !this.messageInput.value.trim();
                });

                // Handle window resize
                window.addEventListener('resize', () => {
                    if (window.innerWidth > 768) {
                        document.getElementById('sidebar').classList.remove('open');
                    }
                });
            }
        }

        // Initialize the chat application
        const chatApp = new MessageMeChat();

        // Utility functions
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                // Redirect to login page or clear session
                window.location.href = '/login'; // Adjust path as needed
            }
        }

        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('open');
        }

        // Simulate random online status changes
        setInterval(() => {
            chatApp.users.forEach(user => {
                if (Math.random() < 0.1) { // 10% chance to change status
                    user.status = user.status === 'online' ? 'offline' : 'online';
                }
            });
            chatApp.renderUsers();
        }, 30000); // Every 30 seconds
    </script>
</body>
</html>