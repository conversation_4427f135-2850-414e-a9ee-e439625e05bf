import { SignInComponent, SignUpComponent } from "./authentication/AuthComponent.js";
import { MainPageComponet } from "./MainPage/MainpageComponent.js";

class AppRouter {
    constructor() {
        this.app = document.querySelector(".app");
        this.currentPage = 'signin';
        this.initialize();
        this.attachEventListeners();
    }

    initialize() {
        const userToken = localStorage.getItem("userToken");

        if (!userToken) {
            this.navigateToAuth('signin');
        } else {
            this.navigateToMain();
        }
    }

    attachEventListeners() {
        // Listen for navigation events
        window.addEventListener('navigate', (event) => {
            const { page } = event.detail;

            switch (page) {
                case 'signin':
                    this.navigateToAuth('signin');
                    break;
                case 'signup':
                    this.navigateToAuth('signup');
                    break;
                case 'main':
                    this.navigateToMain();
                    break;
                default:
                    console.warn('Unknown page:', page);
            }
        });

        // Listen for storage changes (for logout)
        window.addEventListener('storage', (event) => {
            if (event.key === 'userToken' && !event.newValue) {
                this.navigateToAuth('signin');
            }
        });
    }

    navigateToAuth(type = 'signin') {
        this.currentPage = type;

        if (type === 'signup') {
            this.app.innerHTML = SignUpComponent();
        } else {
            this.app.innerHTML = SignInComponent();
        }

        this.initializeCss("./authentication/AuthStyle.css");
        this.initializeScript("./authentication/AuthScript.js");
    }

    navigateToMain() {
        this.currentPage = 'main';
        // Load main application (chat interface)
        this.loadMainApp();
    }

    loadMainApp() {
        // Load the MainPage component
        this.app.innerHTML = MainPageComponet();
        this.initializeCss("./MainPage/MainpageStyle.css");
        this.initializeScript("./MainPage/MainpageScript.js");
    }

    initializeCss(path) {
        let currentStyle = document.querySelector(".dynamicstyles");
        if (currentStyle) {
            document.head.removeChild(currentStyle);
        }

        let css = document.createElement("link");
        css.setAttribute("rel", "stylesheet");
        css.setAttribute("type", "text/css");
        css.setAttribute("href", `${path}`);
        css.setAttribute("class", "dynamicstyles"); // Fixed typo: was "clsss"
        document.head.appendChild(css);
    }

    initializeScript(path) {
        let currentScript = document.querySelector(".dynamicScript");
        if (currentScript) {
            document.head.removeChild(currentScript);
        }

        let script = document.createElement("script");
        script.setAttribute("src", `${path}`);
        script.setAttribute("type", "module"); // Changed to module to support imports
        script.setAttribute("class", "dynamicScript");
        script.defer = true;
        document.head.appendChild(script);
    }
}

// Initialize the app router
const appRouter = new AppRouter();

// Legacy functions removed - now handled by AppRouter class

