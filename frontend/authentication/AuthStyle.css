* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f0f2f5;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.auth-container {
    position: relative;
    width: 400px;
    max-width: 90vw;
    background: #ffffff;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.auth-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo h1 {
    color: #2c3e50;
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 10px;
}

.logo p {
    color: #7f8c8d;
    font-size: 0.9em;
    font-weight: 300;
}

.form-container {
    position: relative;
    overflow: hidden;
}

.demo-info {
    background: #e3f2fd;
    border: 1px solid #667eea;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 20px;
    text-align: center;
}

.demo-info p {
    margin: 0;
    color: #2c3e50;
    font-size: 0.9em;
}

.demo-info code {
    background: #667eea;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
}

.form {
    display: none;
    animation: slideIn 0.5s ease-out;
}

.form.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 0.9em;
}

input[type="text"],
input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    background: #ffffff;
    color: #2c3e50;
    font-size: 16px;
    transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

input::placeholder {
    color: #95a5a6;
}

.submit-btn {
    width: 100%;
    padding: 15px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

.toggle-container {
    text-align: center;
    margin-top: 25px;
}

.toggle-text {
    color: #7f8c8d;
    font-size: 0.9em;
}

.toggle-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.3s ease;
}

.toggle-link:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.social-login {
    margin-top: 30px;
    text-align: center;
}

.social-divider {
    color: #95a5a6;
    margin: 20px 0;
    font-size: 0.9em;
}

.social-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.social-btn {
    flex: 1;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    background: #ffffff;
    color: #2c3e50;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.social-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
    transform: translateY(-1px);
}



.error-message {
    color: #e74c3c;
    font-size: 0.8em;
    margin-top: 5px;
    display: none;
}

.success-message {
    color: #4caf50;
    font-size: 0.8em;
    margin-top: 5px;
    display: none;
}

@media (max-width: 480px) {
    .auth-container {
        padding: 30px 20px;
    }

    .logo h1 {
        font-size: 2em;
    }
}