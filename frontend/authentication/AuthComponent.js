// SignIn Component
export function SignInComponent() {
    return `
    <div class="auth-container">
        <div class="logo">
            <h1>MessageMe</h1>
            <p>Connect, Chat, Share</p>
        </div>

        <div class="form-container">
            <div class="demo-info">
                <p><strong>Demo Login:</strong> Use <code>admin</code> / <code>1234</code></p>
            </div>
            <form id="signin-form" class="form active">
                <div class="form-group">
                    <label for="signin-email">Email Address</label>
                    <input type="email" id="signin-email" placeholder="Enter your email" required>
                    <div class="error-message" id="signin-email-error"></div>
                </div>

                <div class="form-group">
                    <label for="signin-password">Password</label>
                    <input type="password" id="signin-password" placeholder="Enter your password" required>
                    <div class="error-message" id="signin-password-error"></div>
                </div>

                <button type="submit" class="submit-btn">Sign In</button>
                <div class="success-message" id="signin-success"></div>
            </form>
        </div>

        <div class="toggle-container">
            <span class="toggle-text">Don't have an account? </span>
            <a href="#" class="toggle-link" id="signup-link">Sign Up</a>
        </div>

    </div>
    `;
}

// SignUp Component
export function SignUpComponent() {
    return `
    <div class="auth-container">
        <div class="logo">
            <h1>MessageMe</h1>
            <p>Connect, Chat, Share</p>
        </div>

        <div class="form-container">
            <form id="signup-form" class="form active">
                <div class="form-group">
                    <label for="signup-name">Full Name</label>
                    <input type="text" id="signup-name" placeholder="Enter your full name" required>
                    <div class="error-message" id="signup-name-error"></div>
                </div>

                <div class="form-group">
                    <label for="signup-email">Email Address</label>
                    <input type="email" id="signup-email" placeholder="Enter your email" required>
                    <div class="error-message" id="signup-email-error"></div>
                </div>

                <div class="form-group">
                    <label for="signup-password">Password</label>
                    <input type="password" id="signup-password" placeholder="Create a password" required>
                    <div class="error-message" id="signup-password-error"></div>
                </div>

                <div class="form-group">
                    <label for="signup-confirm">Confirm Password</label>
                    <input type="password" id="signup-confirm" placeholder="Confirm your password" required>
                    <div class="error-message" id="signup-confirm-error"></div>
                </div>

                <button type="submit" class="submit-btn">Sign Up</button>
                <div class="success-message" id="signup-success"></div>
            </form>
        </div>

        <div class="toggle-container">
            <span class="toggle-text">Already have an account? </span>
            <a href="#" class="toggle-link" id="signin-link">Sign In</a>
        </div>
    </div>
    `;
}

// Legacy function for backward compatibility
export function Authentication() {
    return SignInComponent();
}